#!/usr/bin/env python3
"""
Test skript pre overenie funkcionality CSV logovania obchodov.
"""

import sys
import os
import logging
from datetime import datetime
import pytz

# Pridaj PY script adresár do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'PY script'))

try:
    import config
    import utils
except ImportError as e:
    print(f"Chyba pri importovaní modulov: {e}")
    print("Uistite sa, že sú súbory config.py a utils.py v adresári 'PY script'")
    sys.exit(1)

def test_csv_logging():
    """Test funkcie append_trade_to_csv"""
    
    print("=== Test CSV logovania obchodov ===\n")
    
    # Inicializuj logovanie
    utils.setup_logging()
    logger = logging.getLogger(__name__)
    
    # Test dáta
    test_trades = [
        {
            'symbol': 'MES',
            'signal': 'LONG',
            'entry_price': 5663.5,
            'exit_price': 5673.5,
            'pnl': 250.0,
            'entry_time': '2025-01-07 14:30:00 UTC',
            'sl_level': 5593.5,
            'trail_activated': False
        },
        {
            'symbol': 'MGC',
            'signal': 'SHORT',
            'entry_price': 2650.8,
            'exit_price': 2640.2,
            'pnl': 318.0,
            'entry_time': '2025-01-07 15:45:00 UTC',
            'sl_level': 2720.8,
            'trail_activated': True
        }
    ]
    
    print("1. Testovanie zápisu obchodov do CSV...")
    
    # Vymaž existujúci CSV súbor ak existuje
    csv_file = getattr(config, 'TRADES_CSV_FILE', 'trades_log.csv')
    if os.path.exists(csv_file):
        os.remove(csv_file)
        print(f"   Vymazaný existujúci súbor: {csv_file}")
    
    # Test zápisu obchodov
    for i, trade in enumerate(test_trades, 1):
        print(f"\n   Test obchod {i}:")
        print(f"   Symbol: {trade['symbol']}, Signal: {trade['signal']}")
        print(f"   Entry: {trade['entry_price']}, Exit: {trade['exit_price']}")
        print(f"   PNL: {trade['pnl']:.2f} USD")
        
        success = utils.append_trade_to_csv(
            trade['symbol'],
            trade['signal'],
            trade['entry_price'],
            trade['exit_price'],
            trade['pnl'],
            trade['entry_time'],
            trade['sl_level'],
            trade['trail_activated']
        )
        
        if success:
            print(f"   ✅ Obchod {i} úspešne zapísaný")
        else:
            print(f"   ❌ Chyba pri zápise obchodu {i}")
    
    print(f"\n2. Kontrola vytvorenia CSV súboru...")
    if os.path.exists(csv_file):
        print(f"   ✅ CSV súbor '{csv_file}' bol vytvorený")
        
        # Prečítaj a zobraz obsah
        print(f"\n3. Obsah CSV súboru:")
        try:
            with open(csv_file, 'r') as f:
                content = f.read()
                print("   " + "="*50)
                for line_num, line in enumerate(content.strip().split('\n'), 1):
                    print(f"   {line_num:2d}: {line}")
                print("   " + "="*50)
        except Exception as e:
            print(f"   ❌ Chyba pri čítaní CSV súboru: {e}")
    else:
        print(f"   ❌ CSV súbor '{csv_file}' nebol vytvorený")
    
    print(f"\n4. Kontrola formátu CSV súboru...")
    try:
        import pandas as pd
        df = pd.read_csv(csv_file)
        print(f"   ✅ CSV súbor má správny formát")
        print(f"   Počet riadkov: {len(df)}")
        print(f"   Stĺpce: {list(df.columns)}")
        
        print(f"\n   Náhľad dát:")
        print(df.to_string(index=False))
        
    except Exception as e:
        print(f"   ❌ Problém s formátom CSV súboru: {e}")
    
    print(f"\n=== Test dokončený ===")
    print(f"CSV súbor: {os.path.abspath(csv_file)}")

if __name__ == '__main__':
    test_csv_logging()
