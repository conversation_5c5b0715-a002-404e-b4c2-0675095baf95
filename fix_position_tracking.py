#!/usr/bin/env python3
"""
Script na opravu problému s trackovaním pozícií po reštarte bota.

Problém: Po reštarte bota sa stráca kontext o otvoren<PERSON>ch pozí<PERSON>ch,
č<PERSON>p<PERSON>bu<PERSON>, že sa neposielajú notifikácie a nezapisujú sa obchody
pri uzatváraní pozícií.

Riešenie: Implementácia persistence stavu pozícií do súboru.
"""

import sys
import os
import json
import logging
from datetime import datetime
import pytz

# Pridanie PY script adresára do sys.path
script_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'PY script')
if script_dir not in sys.path:
    sys.path.insert(0, script_dir)

try:
    import config
    import utils
except ImportError as e:
    print(f"Chyba pri importe: {e}")
    sys.exit(1)

def create_position_persistence_module():
    """Vytvorí modul pre persistence pozícií"""
    
    persistence_code = '''"""
Modul pre persistence stavu pozícií medzi reštartmi bota.
"""

import json
import os
import logging
from datetime import datetime
import pytz

logger = logging.getLogger(__name__)

POSITIONS_STATE_FILE = 'positions_state.json'

def save_position_state(instruments_data):
    """
    Uloží stav pozícií do súboru.
    
    Args:
        instruments_data: List inštrumentov s ich stavom
    """
    try:
        state_data = {}
        
        for inst in instruments_data:
            if inst.get('inPosition', False):
                symbol = inst.get('symbol', 'UNKNOWN')
                state_data[symbol] = {
                    'inPosition': True,
                    'entry_signal': inst.get('entry_signal', ''),
                    'entry_price': inst.get('entry_price', 0.0),
                    'entry_time': inst.get('entry_time', ''),
                    'entry_timestamp': inst.get('entry_timestamp', 0.0),
                    'sl_price': inst.get('sl_price', 0.0),
                    'sl_order_id': inst.get('sl_order_id'),
                    'trailing_set': inst.get('trailing_set', False),
                    'multiplier': inst.get('multiplier', 1),
                    'saved_at': datetime.now(pytz.utc).isoformat()
                }
        
        with open(POSITIONS_STATE_FILE, 'w') as f:
            json.dump(state_data, f, indent=2)
        
        logger.info(f"Stav pozícií uložený: {len(state_data)} aktívnych pozícií")
        return True
        
    except Exception as e:
        logger.error(f"Chyba pri ukladaní stavu pozícií: {e}")
        return False

def load_position_state():
    """
    Načíta stav pozícií zo súboru.
    
    Returns:
        dict: Stav pozícií alebo prázdny dict
    """
    try:
        if not os.path.exists(POSITIONS_STATE_FILE):
            logger.info("Súbor so stavom pozícií neexistuje")
            return {}
        
        with open(POSITIONS_STATE_FILE, 'r') as f:
            state_data = json.load(f)
        
        logger.info(f"Načítaný stav pozícií: {len(state_data)} pozícií")
        return state_data
        
    except Exception as e:
        logger.error(f"Chyba pri načítavaní stavu pozícií: {e}")
        return {}

def restore_position_state(instruments_data, ib):
    """
    Obnoví stav pozícií po reštarte bota.
    
    Args:
        instruments_data: List inštrumentov
        ib: IB connection objekt
    """
    try:
        saved_state = load_position_state()
        if not saved_state:
            return
        
        # Získanie aktuálnych pozícií z IB
        current_positions = ib.positions()
        
        for inst in instruments_data:
            symbol = inst.get('symbol', '')
            if symbol in saved_state:
                saved_pos = saved_state[symbol]
                
                # Kontrola, či pozícia stále existuje v IB
                ib_position = None
                for pos in current_positions:
                    if (pos.contract.symbol == symbol and 
                        pos.position != 0):
                        ib_position = pos
                        break
                
                if ib_position:
                    # Pozícia stále existuje, obnovíme stav
                    inst.update({
                        'inPosition': True,
                        'entry_signal': saved_pos.get('entry_signal', ''),
                        'entry_price': saved_pos.get('entry_price', 0.0),
                        'entry_time': saved_pos.get('entry_time', ''),
                        'entry_timestamp': saved_pos.get('entry_timestamp', 0.0),
                        'sl_price': saved_pos.get('sl_price', 0.0),
                        'sl_order_id': saved_pos.get('sl_order_id'),
                        'trailing_set': saved_pos.get('trailing_set', False)
                    })
                    
                    logger.info(f"[{symbol}] Obnovený stav pozície: {saved_pos.get('entry_signal')} @ {saved_pos.get('entry_price')}")
                else:
                    # Pozícia už neexistuje, bola zatvorená počas reštartu
                    logger.warning(f"[{symbol}] Pozícia bola zatvorená počas reštartu - nebola zalogovaná!")
                    
                    # Pokúsime sa zalogovat zatvorenie
                    log_missed_closure(symbol, saved_pos, ib)
        
        # Vyčistenie starého stavu
        clear_position_state()
        
    except Exception as e:
        logger.error(f"Chyba pri obnovovaní stavu pozícií: {e}")

def log_missed_closure(symbol, saved_pos, ib):
    """
    Zaloguje zatvorenie pozície, ktoré sa stalo počas reštartu.
    
    Args:
        symbol: Symbol inštrumentu
        saved_pos: Uložený stav pozície
        ib: IB connection objekt
    """
    try:
        # Pokúsime sa nájsť exit cenu z fills
        fills = ib.fills()
        exit_price = None
        
        entry_timestamp = saved_pos.get('entry_timestamp', 0)
        if entry_timestamp > 0:
            entry_dt = datetime.fromtimestamp(entry_timestamp, tz=pytz.utc)
            
            for fill in reversed(fills):
                if (fill.contract.symbol == symbol and
                    fill.execution.time >= entry_dt):
                    
                    # Kontrola smeru
                    entry_signal = saved_pos.get('entry_signal', '')
                    if ((entry_signal == 'LONG' and fill.execution.side == 'SLD') or
                        (entry_signal == 'SHORT' and fill.execution.side == 'BOT')):
                        exit_price = fill.execution.price
                        break
        
        # Ak nenájdeme exit cenu, použijeme entry cenu
        if exit_price is None:
            exit_price = saved_pos.get('entry_price', 0.0)
            logger.warning(f"[{symbol}] Exit cena nenájdená, používam entry cenu")
        
        # Výpočet PNL
        entry_price = saved_pos.get('entry_price', 0.0)
        entry_signal = saved_pos.get('entry_signal', '')
        multiplier = saved_pos.get('multiplier', 1)
        
        if entry_signal == 'LONG':
            pnl_pts = exit_price - entry_price
        else:
            pnl_pts = entry_price - exit_price
        
        pnl_usd = pnl_pts * multiplier * getattr(config, 'QUANTITY', 1)
        
        # Odoslanie notifikácie
        msg = f"Uzavretý {entry_signal} na {symbol} @ {exit_price:.2f}, P/L {pnl_usd:.2f} USD (počas reštartu)"
        utils.send_telegram(msg)
        
        # Zápis do CSV
        utils.append_trade_to_csv(
            symbol, entry_signal, entry_price, exit_price, pnl_usd,
            saved_pos.get('entry_time', 'RESTART'), 
            saved_pos.get('sl_price', 0.0),
            saved_pos.get('trailing_set', False)
        )
        
        logger.info(f"[{symbol}] Zalogované zatvorenie počas reštartu: PNL {pnl_usd:.2f} USD")
        
    except Exception as e:
        logger.error(f"[{symbol}] Chyba pri logovaní zatvorenia počas reštartu: {e}")

def clear_position_state():
    """Vymaže súbor so stavom pozícií"""
    try:
        if os.path.exists(POSITIONS_STATE_FILE):
            os.remove(POSITIONS_STATE_FILE)
            logger.info("Súbor so stavom pozícií vymazaný")
    except Exception as e:
        logger.error(f"Chyba pri mazaní súboru so stavom pozícií: {e}")
'''
    
    # Uloženie modulu
    module_path = os.path.join('PY script', 'position_persistence.py')
    with open(module_path, 'w', encoding='utf-8') as f:
        f.write(persistence_code)
    
    print(f"✅ Vytvorený modul: {module_path}")

def create_integration_patch():
    """Vytvorí patch pre integráciu persistence do hlavného bota"""
    
    patch_instructions = '''
INŠTRUKCIE PRE INTEGRÁCIU POSITION PERSISTENCE:

1. Import modulu na začiatok camarilla.py:
   from position_persistence import save_position_state, restore_position_state

2. Po úspešnom pripojení k IB (okolo riadku 663):
   # Obnovenie stavu pozícií po reštarte
   restore_position_state(instruments_data_list, ib)

3. Pred odpojením od IB (okolo riadku 731):
   # Uloženie stavu pozícií pred odpojením
   save_position_state(instruments_data_list)

4. Po úspešnom uzavretí pozície (okolo riadku 614):
   # Aktualizácia uloženého stavu
   save_position_state(instruments_data_list)

ALTERNATÍVNE RIEŠENIE (bez úpravy hlavného kódu):
Spustiť monitoring script, ktorý bude sledovať pozície a automaticky
logovať zatvorenia, ktoré bot nezachytil.
'''
    
    with open('position_persistence_integration.txt', 'w', encoding='utf-8') as f:
        f.write(patch_instructions)
    
    print("✅ Vytvorené inštrukcie: position_persistence_integration.txt")

def main():
    """Hlavná funkcia"""
    print("🔧 OPRAVA PROBLÉMU S TRACKOVANÍM POZÍCIÍ")
    print("=" * 50)
    
    # Nastavenie logovania
    utils.setup_logging()
    
    print("Vytváram modul pre persistence pozícií...")
    create_position_persistence_module()
    
    print("Vytváram inštrukcie pre integráciu...")
    create_integration_patch()
    
    print("\n✅ OPRAVA DOKONČENÁ!")
    print("\nĎalšie kroky:")
    print("1. Skontrolujte súbor position_persistence_integration.txt")
    print("2. Integrujte persistence do hlavného bota")
    print("3. Alebo spustite monitoring script")
    print("4. Reštartujte trading bot")

if __name__ == '__main__':
    main()
