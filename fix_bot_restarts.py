#!/usr/bin/env python3
"""
Oprava zbytočných reštartov trading bota.

Problém: Bot sa po každej hodinovej sviečke odpojí a znova pripojí,
čím stráca kontext o pozíciách.

Riešenie: Upraviť logiku tak, aby bot zostal pripojený a len čakal
na ďalšiu sviečku bez odpájania.
"""

import os
import shutil
from datetime import datetime

def backup_original_files():
    """Vytvorí zálohu pôvodných súborov"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup_{timestamp}"
    
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    files_to_backup = [
        'PY script/camarilla.py',
        'PY script/utils.py'
    ]
    
    for file_path in files_to_backup:
        if os.path.exists(file_path):
            backup_path = os.path.join(backup_dir, os.path.basename(file_path))
            shutil.copy2(file_path, backup_path)
            print(f"✅ Zálohovaný: {file_path} -> {backup_path}")
    
    return backup_dir

def create_improved_main_loop():
    """Vytvorí vylepšenú hlavnú slučku bez zbytočných reštartov"""
    
    improved_code = '''
def run_main_trading_loop_improved():
    """
    Vylepšená hlavná slučka bez zbytočných reštartov.
    Bot zostane pripojený a len čaká na ďalšie sviečky.
    """
    global last_closed_conids_session, current_active_expiry, instruments_data_list, ib
    global theoretical_new_bar_start_utc, needs_data_initialization

    logger.info("Spúšťam vylepšenú hlavnú slučku bez reštartov...")
    
    # Inicializácia ak je potrebná
    if needs_data_initialization:
        logger.info("run_main_trading_loop_improved: Potrebná (re)inicializácia dát inštrumentov.")
        initialize_instruments_data()

    # Hlavná slučka - beží donekonečna bez odpájania
    while True:
        try:
            # Kontrola pripojenia
            if not ib.isConnected():
                logger.warning("Stratené pripojenie k IB, pokúšam sa znova pripojiť...")
                try:
                    ib.connect(config.IB_HOST, config.IB_PORT, clientId=config.IB_CLIENT_ID, timeout=20)
                    if ib.isConnected():
                        logger.info("Pripojenie k IB obnovené")
                        if needs_data_initialization:
                            initialize_instruments_data()
                    else:
                        logger.error("Nepodarilo sa obnoviť pripojenie, čakám 30s...")
                        time.sleep(30)
                        continue
                except Exception as e:
                    logger.error(f"Chyba pri obnove pripojenia: {e}")
                    time.sleep(30)
                    continue

            # Získanie aktívnych inštrumentov
            active_instruments_to_process = [inst for inst in instruments_data_list if inst.get('contract')]
            if not active_instruments_to_process:
                logger.error("Žiadne platné inštrumenty s kontraktami. Čakám 60s a reinicializujem.")
                needs_data_initialization = True
                time.sleep(60)
                continue

            # Čakanie na ďalšiu sviečku (BEZ ODPÁJANIA!)
            if theoretical_new_bar_start_utc is None:
                logger.info("Prvá iterácia, čakám na prvú celú sviečku...")

            theoretical_new_bar_start_utc = utils.wait_for_next_bar(config.TIMEFRAME)
            logger.info(f"Nová perióda začína: {theoretical_new_bar_start_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")

            # Spracovanie všetkých inštrumentov
            process_all_instruments(active_instruments_to_process, theoretical_new_bar_start_utc)
            
            # Krátka pauza pred ďalšou iteráciou
            time.sleep(1)
            
        except KeyboardInterrupt:
            logger.info("Bot ukončený manuálne")
            break
        except Exception as e:
            logger.error(f"Chyba v hlavnej slučke: {e}", exc_info=True)
            # Namiesto reštartu len čakáme a pokračujeme
            time.sleep(10)
            continue

def process_all_instruments(active_instruments_to_process, theoretical_new_bar_start_utc):
    """
    Spracuje všetky inštrumenty pre aktuálnu sviečku.
    Vyčlenené do samostatnej funkcie pre lepšiu čitateľnosť.
    """
    # Tu by bol pôvodný kód spracovania inštrumentov
    # (riadky 185-631 z pôvodnej run_main_trading_loop)
    
    tf_parts = config.TIMEFRAME.split()
    tf_value = int(tf_parts[0]) if len(tf_parts) > 0 and tf_parts[0].isdigit() else 1
    tf_unit = tf_parts[1].lower() if len(tf_parts) > 1 else "hour"
    if 'hour' in tf_unit: 
        timeframe_delta = timedelta(hours=tf_value)
    elif 'min' in tf_unit: 
        timeframe_delta = timedelta(minutes=tf_value)
    else: 
        timeframe_delta = timedelta(hours=1)
        logger.error(f"Neznámy TIMEFRAME unit: {config.TIMEFRAME}")

    expected_signal_bar_start_utc = theoretical_new_bar_start_utc - timeframe_delta
    logger.info(f"Očakávaný začiatok signálnej sviečky: {expected_signal_bar_start_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")

    # EOD kontrola
    if config.CHECK_EOD_CLOSURE_ENABLED and utils.is_near_market_close():
        handle_eod_closure(active_instruments_to_process)

    # Spracovanie každého inštrumentu
    for inst_data in active_instruments_to_process:
        try:
            process_single_instrument(inst_data, theoretical_new_bar_start_utc, expected_signal_bar_start_utc)
        except Exception as e:
            symbol = inst_data.get('symbol', 'UNKNOWN')
            logger.error(f"[{symbol}] Chyba pri spracovaní inštrumentu: {e}", exc_info=True)
            continue

def handle_eod_closure(active_instruments_to_process):
    """Spracuje EOD uzatváranie pozícií"""
    logger.info("EOD CHECK: Kontrolujem pozície na uzavretie.")
    # Tu by bol pôvodný EOD kód (riadky 185-274)
    pass

def process_single_instrument(inst_data, theoretical_new_bar_start_utc, expected_signal_bar_start_utc):
    """Spracuje jeden inštrument"""
    symbol = inst_data['symbol']
    contract_obj = inst_data['contract']
    
    if inst_data.get('last_bar_processed_utc') == theoretical_new_bar_start_utc:
        logger.debug(f"[{symbol}] Signál už spracovaný, preskakujem.")
        return
    
    logger.info(f"--- Spracovávam {symbol} ---")
    # Tu by bol pôvodný kód spracovania inštrumentu (riadky 286-631)
    
    # Označenie ako spracované
    inst_data['last_bar_processed_utc'] = theoretical_new_bar_start_utc
'''
    
    return improved_code

def create_patch_instructions():
    """Vytvorí inštrukcie na aplikovanie opravy"""
    
    instructions = '''
INŠTRUKCIE PRE OPRAVU ZBYTOČNÝCH REŠTARTOV:

PROBLÉM:
Bot sa po každej hodinovej sviečke odpojí a znova pripojí (riadky 730-737),
čím stráca kontext o pozíciách a nezachytáva uzatvárania.

RIEŠENIE:
1. Nahradiť pôvodnú hlavnú slučku vylepšenou verziou
2. Odstrániť zbytočné odpájanie po každej sviečke
3. Pridať lepšie error handling

KROKY:
1. Záloha je vytvorená automaticky
2. V camarilla.py nahradiť funkciu run_main_trading_loop()
3. V __main__ sekcii nahradiť volanie run_main_trading_loop() 
   za run_main_trading_loop_improved()
4. Odstrániť riadky 730-737 (odpájanie pred reštartom)

ALTERNATÍVA:
Ak nechcete upravovať hlavný kód, použite position_monitor.py
ktorý bude zachytávať chýbajúce uzatvárania.

TESTOVANIE:
1. Spustite opravený bot
2. Otvorte pozíciu
3. Nechajte ju zatvoriť cez SL
4. Skontrolujte, či prišla Telegram notifikácia a zápis do CSV
'''
    
    with open('restart_fix_instructions.txt', 'w', encoding='utf-8') as f:
        f.write(instructions)

def main():
    """Hlavná funkcia opravy"""
    print("🔧 OPRAVA ZBYTOČNÝCH REŠTARTOV TRADING BOTA")
    print("=" * 60)
    
    print("Analyzujem problém...")
    print("✅ Identifikovaný problém: Bot sa odpája po každej sviečke")
    print("✅ Príčina: Riadky 730-737 v camarilla.py")
    print("✅ Dôsledok: Strata kontextu pozícií")
    
    print("\nVytváram zálohu súborov...")
    backup_dir = backup_original_files()
    print(f"✅ Záloha vytvorená v: {backup_dir}")
    
    print("\nVytváram vylepšenú hlavnú slučku...")
    improved_code = create_improved_main_loop()
    
    with open('improved_main_loop.py', 'w', encoding='utf-8') as f:
        f.write(improved_code)
    print("✅ Vylepšená slučka uložená: improved_main_loop.py")
    
    print("\nVytváram inštrukcie...")
    create_patch_instructions()
    print("✅ Inštrukcie vytvorené: restart_fix_instructions.txt")
    
    print("\n" + "=" * 60)
    print("🎯 ODPORÚČANIA:")
    print("1. OKAMŽITÉ: Spustite position_monitor.py paralelne s botom")
    print("2. DLHODOBÉ: Aplikujte opravu podľa restart_fix_instructions.txt")
    print("3. TESTOVANIE: Otvorte pozíciu a nechajte ju zatvoriť")
    
    print("\n💡 OKAMŽITÉ SPUSTENIE MONITORU:")
    print("   python position_monitor.py")
    print("   (v novom termináli/okne)")

if __name__ == '__main__':
    main()
