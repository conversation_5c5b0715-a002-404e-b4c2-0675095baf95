#!/usr/bin/env python3
"""
Monitoring script pre sledovanie pozícií a automatické logovanie zatvorení.

Tento script beží paralelne s hlavným botom a sleduje pozície v IB.
Keď detekuje zatvorenie pozície, automaticky pošle Telegram notifikáciu
a zapíše obchod do CSV, ak to hlavný bot nestihl.
"""

import sys
import os
import time
import json
import logging
from datetime import datetime, timedelta
import pytz

# Pridanie PY script adresára do sys.path
script_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'PY script')
if script_dir not in sys.path:
    sys.path.insert(0, script_dir)

try:
    import config
    import utils
    from ib_insync import IB, util
except ImportError as e:
    print(f"Chyba pri importe: {e}")
    print("Uistite sa, že máte nainštalované všetky potrebné knižnice.")
    sys.exit(1)

# Globálne premenné
ib = IB()
logger = logging.getLogger(__name__)
MONITOR_STATE_FILE = 'position_monitor_state.json'
CHECK_INTERVAL = 10  # sekúnd medzi kontrolami

class PositionMonitor:
    def __init__(self):
        self.tracked_positions = {}
        self.last_check_time = datetime.now(pytz.utc)
        self.load_state()
    
    def load_state(self):
        """Načíta uložený stav monitoru"""
        try:
            if os.path.exists(MONITOR_STATE_FILE):
                with open(MONITOR_STATE_FILE, 'r') as f:
                    data = json.load(f)
                    self.tracked_positions = data.get('tracked_positions', {})
                    logger.info(f"Načítaný stav: {len(self.tracked_positions)} sledovaných pozícií")
        except Exception as e:
            logger.error(f"Chyba pri načítavaní stavu: {e}")
            self.tracked_positions = {}
    
    def save_state(self):
        """Uloží stav monitoru"""
        try:
            data = {
                'tracked_positions': self.tracked_positions,
                'last_update': datetime.now(pytz.utc).isoformat()
            }
            with open(MONITOR_STATE_FILE, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Chyba pri ukladaní stavu: {e}")
    
    def update_positions(self, current_positions):
        """Aktualizuje sledované pozície"""
        current_symbols = set()
        
        for pos in current_positions:
            if pos.position != 0:
                symbol = pos.contract.symbol
                current_symbols.add(symbol)
                
                if symbol not in self.tracked_positions:
                    # Nová pozícia
                    self.tracked_positions[symbol] = {
                        'position_size': pos.position,
                        'avg_cost': pos.avgCost,
                        'detected_at': datetime.now(pytz.utc).isoformat(),
                        'contract_id': pos.contract.conId,
                        'multiplier': getattr(pos.contract, 'multiplier', '1')
                    }
                    logger.info(f"[{symbol}] Nová pozícia detekovaná: {pos.position} @ {pos.avgCost}")
                else:
                    # Aktualizácia existujúcej pozície
                    self.tracked_positions[symbol].update({
                        'position_size': pos.position,
                        'avg_cost': pos.avgCost
                    })
        
        # Kontrola zatvorených pozícií
        closed_symbols = set(self.tracked_positions.keys()) - current_symbols
        for symbol in closed_symbols:
            self.handle_position_closure(symbol)
            del self.tracked_positions[symbol]
        
        self.save_state()
    
    def handle_position_closure(self, symbol):
        """Spracuje zatvorenie pozície"""
        try:
            pos_data = self.tracked_positions[symbol]
            logger.info(f"[{symbol}] Detekované zatvorenie pozície")
            
            # Pokúsime sa nájsť exit cenu z fills
            exit_price = self.find_exit_price(symbol, pos_data)
            entry_price = pos_data['avg_cost']
            position_size = pos_data['position_size']
            multiplier = float(pos_data.get('multiplier', 1))
            
            # Určenie smeru obchodu
            entry_signal = 'LONG' if position_size > 0 else 'SHORT'
            
            # Výpočet PNL
            if entry_signal == 'LONG':
                pnl_pts = exit_price - entry_price
            else:
                pnl_pts = entry_price - exit_price
            
            pnl_usd = pnl_pts * multiplier * abs(position_size)
            
            # Formátovanie ceny podľa typu inštrumentu
            price_format = ".4f" if symbol in ['M6A', 'M6B', 'M6E'] else ".2f"
            
            # Odoslanie Telegram notifikácie
            msg = f"🤖 Monitor: Uzavretý {entry_signal} na {symbol} @ {exit_price:{price_format}}, P/L {pnl_usd:.2f} USD"
            telegram_success = utils.send_telegram(msg)
            
            # Zápis do CSV
            csv_success = utils.append_trade_to_csv(
                symbol, entry_signal, entry_price, exit_price, pnl_usd,
                pos_data.get('detected_at', 'MONITOR'), 0.0, False
            )
            
            logger.info(f"[{symbol}] Zatvorenie zalogované - TG: {telegram_success}, CSV: {csv_success}")
            
        except Exception as e:
            logger.error(f"[{symbol}] Chyba pri spracovaní zatvorenia: {e}")
    
    def find_exit_price(self, symbol, pos_data):
        """Nájde exit cenu z fills alebo použije aktuálnu cenu"""
        try:
            # Pokúsime sa nájsť v fills
            fills = ib.fills()
            detected_time = datetime.fromisoformat(pos_data['detected_at'].replace('Z', '+00:00'))
            
            for fill in reversed(fills):
                if (fill.contract.symbol == symbol and 
                    fill.execution.time >= detected_time):
                    
                    position_size = pos_data['position_size']
                    # Kontrola smeru zatvorenia
                    if ((position_size > 0 and fill.execution.side == 'SLD') or
                        (position_size < 0 and fill.execution.side == 'BOT')):
                        return fill.execution.price
            
            # Ak nenájdeme v fills, použijeme aktuálnu cenu
            contract = self.get_contract_for_symbol(symbol)
            if contract:
                ticker = ib.reqMktData(contract, '', False, False)
                ib.sleep(2)  # Čakanie na dáta
                
                if ticker.last and ticker.last > 0:
                    return ticker.last
                elif ticker.bid and ticker.ask:
                    return (ticker.bid + ticker.ask) / 2
            
            # Fallback na entry cenu
            return pos_data['avg_cost']
            
        except Exception as e:
            logger.error(f"[{symbol}] Chyba pri hľadaní exit ceny: {e}")
            return pos_data['avg_cost']
    
    def get_contract_for_symbol(self, symbol):
        """Získa kontrakt pre symbol"""
        try:
            for inst_config in config.INSTRUMENTS_CONFIG:
                if inst_config['symbol'] == symbol:
                    # Použijeme utils funkciu pre získanie najlepšieho kontraktu
                    return utils.get_best_contract_for_instrument(ib, inst_config)
            return None
        except Exception as e:
            logger.error(f"Chyba pri získavaní kontraktu pre {symbol}: {e}")
            return None

def connect_to_ib():
    """Pripojenie k IB"""
    try:
        if not ib.isConnected():
            logger.info(f"Pripájam sa k IB na {config.IB_HOST}:{config.IB_PORT}")
            ib.connect(config.IB_HOST, config.IB_PORT, clientId=config.IB_CLIENT_ID + 10)  # Iné client ID
            logger.info("Úspešne pripojený k IB")
        return True
    except Exception as e:
        logger.error(f"Chyba pri pripájaní k IB: {e}")
        return False

def main():
    """Hlavná funkcia monitoru"""
    print("🔍 POSITION MONITOR - Sledovanie pozícií")
    print("=" * 50)
    
    # Nastavenie logovania
    utils.setup_logging()
    logger.info("Position Monitor spustený")
    
    # Odoslanie štartovacej notifikácie
    utils.send_telegram("🔍 Position Monitor spustený - sleduje pozície pre chýbajúce notifikácie")
    
    monitor = PositionMonitor()
    
    try:
        while True:
            if not connect_to_ib():
                logger.warning("Nie je pripojenie k IB, čakám 30 sekúnd...")
                time.sleep(30)
                continue
            
            try:
                # Získanie aktuálnych pozícií
                current_positions = ib.positions()
                monitor.update_positions(current_positions)
                
                # Zobrazenie stavu
                active_count = len([p for p in current_positions if p.position != 0])
                logger.debug(f"Aktívnych pozícií: {active_count}, Sledovaných: {len(monitor.tracked_positions)}")
                
            except Exception as e:
                logger.error(f"Chyba pri kontrole pozícií: {e}")
            
            # Čakanie do ďalšej kontroly
            time.sleep(CHECK_INTERVAL)
            
    except KeyboardInterrupt:
        logger.info("Monitor ukončený manuálne")
        utils.send_telegram("🛑 Position Monitor ukončený")
    except Exception as e:
        logger.error(f"Kritická chyba v monitore: {e}")
        utils.send_telegram(f"❌ Position Monitor spadol: {e}")
    finally:
        if ib.isConnected():
            ib.disconnect()
        logger.info("Position Monitor ukončený")

if __name__ == '__main__':
    main()
