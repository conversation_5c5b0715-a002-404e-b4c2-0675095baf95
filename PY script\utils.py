# utils.py

import logging
import time
from datetime import datetime, timedelta
import requests
import pandas as pd
import pytz
import sys
import os

import config

logger = logging.getLogger(__name__)

def setup_logging():
    log_level = getattr(config, 'LOG_LEVEL', 'INFO').upper()
    log_format = getattr(config, 'LOG_FORMAT', '%(asctime)s %(levelname)s:%(filename)s:%(lineno)d:%(name)s:%(message)s')
    log_date_format = getattr(config, 'LOG_DATE_FORMAT', '%Y-%m-%d %H:%M:%S')

    numeric_level = getattr(logging, log_level, logging.INFO)

    # Nastavenie handlerov podľa konfigurácie
    handlers = [logging.StreamHandler(sys.stdout)]
    if getattr(config, 'LOG_TO_FILE', True):
        handlers.append(logging.FileHandler('camarilla.log'))

    logging.basicConfig(
        level=numeric_level,
        format=log_format,
        datefmt=log_date_format,
        handlers=handlers
    )

    logger.info(f"Logging nastavený na úroveň: {log_level}")

def append_trade_to_csv(symbol, signal, entry_price, exit_price, pnl, entry_time, sl_level, trail_activated):
    """
    Zapíše obchod do CSV súboru.

    Args:
        symbol: Symbol inštrumentu (napr. 'MES', 'MGC')
        signal: Smer obchodu ('LONG', 'SHORT')
        entry_price: Vstupná cena
        exit_price: Výstupná cena
        pnl: Profit/Loss v USD
        entry_time: Čas vstupu do obchodu
        sl_level: Úroveň stop-loss
        trail_activated: Či bol aktivovaný trailing stop (True/False)

    Returns:
        bool: True ak bol zápis úspešný, False ak nie
    """
    try:
        csv_file = getattr(config, 'TRADES_CSV_FILE', 'trades_log.csv')

        if not os.path.exists(csv_file):
            header = "symbol,signal,entry_price,exit_price,pnl,entry_time,sl_level,trail_activated\n"
            with open(csv_file, 'w') as f:
                f.write(header)

        with open(csv_file, 'a') as f:
            f.write(f"{symbol},{signal},{entry_price},{exit_price},{pnl},{entry_time},{sl_level},{trail_activated}\n")

        logger.info(f"Trade zapísaný do CSV: {symbol} {signal} entry={entry_price} exit={exit_price} pnl={pnl:.2f}")
        return True

    except Exception as e:
        logger.error(f"Chyba pri zápise trade do CSV: {e}")
        return False

def send_telegram(message: str) -> bool:
    try:
        token = getattr(config, 'TELEGRAM_TOKEN', None)
        chat_id = getattr(config, 'TELEGRAM_CHAT_ID', None)

        if not token or not chat_id:
            logger.warning("Telegram token alebo chat_id nie sú nastavené v config.py")
            return False

        url = f"https://api.telegram.org/bot{token}/sendMessage"
        data = {
            'chat_id': chat_id,
            'text': message,
            'parse_mode': 'HTML'
        }

        response = requests.post(url, data=data, timeout=10)

        if response.status_code == 200:
            logger.debug(f"Telegram správa odoslaná úspešne: {message[:50]}...")
            return True
        else:
            logger.error(f"Chyba pri odosielaní Telegram správy. Status: {response.status_code}, Response: {response.text}")
            return False

    except requests.exceptions.Timeout:
        logger.error("Timeout pri odosielaní Telegram správy")
        return False
    except requests.exceptions.RequestException as e:
        logger.error(f"Chyba pri HTTP požiadavke na Telegram: {e}")
        return False
    except Exception as e_gen:
        logger.error(f"Neočakávaná chyba pri odosielaní Telegramu: {e_gen}", exc_info=True)
    return False

def get_front_contract_month():
    """DEPRECATED: Stará funkcia pre kvartálne kontrakty. Používa sa len pre spätnosť."""
    now = datetime.now()
    year = now.year
    month = now.month
    day = now.day

    # Pre kvartálne (H,M,U,Z) - Mar, Jun, Sep, Dec
    # Rollover sa často deje predchádzajúci mesiac, napr. druhý piatok (okolo 8.-14. dňa)
    if (month == 2 and day > 8) or month == 3 : target_q_month = 3
    elif (month == 5 and day > 8) or month == 6: target_q_month = 6
    elif (month == 8 and day > 8) or month == 9: target_q_month = 9
    elif (month == 11 and day > 8) or month == 12: target_q_month = 12
    else:
        if month < 3: target_q_month = 3
        elif month < 6: target_q_month = 6
        elif month < 9: target_q_month = 9
        else:
            target_q_month = 12
            if month == 12 and day > 8:
                 year +=1; target_q_month = 3

    if month > target_q_month and target_q_month == 3 and month >=10:
         year += 1

    logger.debug(f"get_front_contract_month: now={now.strftime('%Y-%m-%d')}, target_q_month={target_q_month}, year={year}")
    return f"{year}{target_q_month:02d}"


def get_best_contract_for_instrument(ib, symbol, exchange, currency='USD'):
    """
    Nájde najlikvidnejší kontrakt pre daný nástroj.

    Logika výberu:
    - Ak front month má viac ako 4 dni do expirácie → vyber ho
    - Ak front month má 4 alebo menej dní → vyber ďalší kontrakt

    Args:
        ib: IB connection object
        symbol: Symbol nástroja (napr. 'MGC', 'MES')
        exchange: Burza (napr. 'COMEX', 'CME')
        currency: Mena (default 'USD')

    Returns:
        Contract object alebo None ak sa nenašiel vhodný kontrakt
    """
    from ib_insync import Future
    from datetime import datetime, timezone, timedelta

    try:
        logger.info(f"[{symbol}] Hľadám najlepší kontrakt na {exchange}...")

        # Vytvor generický kontrakt
        generic_contract = Future(symbol=symbol, exchange=exchange, currency=currency)

        # Získaj všetky dostupné kontrakty
        all_contracts = ib.reqContractDetails(generic_contract)

        if not all_contracts:
            logger.error(f"[{symbol}] Nenašiel som žiadne kontrakty na {exchange}")
            return None

        logger.debug(f"[{symbol}] Našiel som {len(all_contracts)} kontraktov")

        # Filtrovanie kontraktov
        now = datetime.now(timezone.utc)
        valid_contracts = []

        for contract_detail in all_contracts:
            contract = contract_detail.contract

            try:
                # Parsuj dátum expirácie
                expiry_str = contract.lastTradeDateOrContractMonth
                if len(expiry_str) == 8:  # YYYYMMDD
                    expiry_date = datetime.strptime(expiry_str, '%Y%m%d').replace(tzinfo=timezone.utc)
                elif len(expiry_str) == 6:  # YYYYMM
                    expiry_date = datetime.strptime(expiry_str + '01', '%Y%m%d').replace(tzinfo=timezone.utc)
                    # Pre YYYYMM, nastav na koniec mesiaca
                    next_month = expiry_date.replace(day=28) + timedelta(days=4)
                    expiry_date = next_month - timedelta(days=next_month.day)
                else:
                    logger.warning(f"[{symbol}] Neznámy formát dátumu expirácie: {expiry_str}")
                    continue

                # Kontrola či kontrakt nie je expirovaný
                days_to_expiry = (expiry_date - now).days

                # Filtrovanie:
                # 1. Kontrakt nesmie byť expirovaný
                # 2. Kontrakt nesmie expírovať príliš skoro (min 7 dní)
                # 3. Kontrakt nesmie expírovať príliš ďaleko (max 18 mesiacov)
                if days_to_expiry < 7:
                    logger.debug(f"[{symbol}] Preskakujem {contract.localSymbol} - expiruje príliš skoro ({days_to_expiry} dní)")
                    continue
                elif days_to_expiry > 540:  # 18 mesiacov
                    logger.debug(f"[{symbol}] Preskakujem {contract.localSymbol} - expiruje príliš ďaleko ({days_to_expiry} dní)")
                    continue

                valid_contracts.append({
                    'contract': contract,
                    'expiry_date': expiry_date,
                    'days_to_expiry': days_to_expiry,
                    'local_symbol': contract.localSymbol
                })

                logger.debug(f"[{symbol}] Kandidát: {contract.localSymbol} - expiruje za {days_to_expiry} dní")

            except Exception as e:
                logger.warning(f"[{symbol}] Chyba pri spracovaní kontraktu {contract.localSymbol}: {e}")
                continue

        if not valid_contracts:
            logger.error(f"[{symbol}] Nenašiel som žiadne platné kontrakty")
            return None

        # Zoraď kontrakty podľa dátumu expirácie (najbližší najskôr)
        valid_contracts.sort(key=lambda x: x['expiry_date'], reverse=False)

        # Logika výberu najlikvidnejšieho kontraktu:
        # 1. Ak front month má viac ako 4 dni do expirácie → vyber ho
        # 2. Ak front month má 4 alebo menej dní → vyber ďalší kontrakt

        best_contract = None
        rollover_threshold_days = 4

        for contract_info in valid_contracts:
            if contract_info['days_to_expiry'] > rollover_threshold_days:
                best_contract = contract_info
                logger.info(f"[{symbol}] Vybraný aktívny kontrakt: {contract_info['local_symbol']} "
                           f"(expiruje za {contract_info['days_to_expiry']} dní)")
                break

        # Ak žiadny kontrakt nemá viac ako 4 dni, vyber najbližší dostupný
        if best_contract is None and valid_contracts:
            best_contract = valid_contracts[0]
            logger.warning(f"[{symbol}] Žiadny kontrakt nemá viac ako {rollover_threshold_days} dní, "
                          f"vyberám najbližší: {best_contract['local_symbol']} "
                          f"(expiruje za {best_contract['days_to_expiry']} dní)")

        if best_contract is None:
            logger.error(f"[{symbol}] Nepodarilo sa vybrať žiadny vhodný kontrakt")
            return None

        return best_contract['contract']

    except Exception as e:
        logger.error(f"[{symbol}] Chyba pri hľadaní najlepšieho kontraktu: {e}")
        return None


def calc_pivots(df_daily: pd.DataFrame, instrument_symbol: str):
    if df_daily is None or len(df_daily) < 2:
        logger.warning(f"[{instrument_symbol}] Nedostatok denných dát na výpočet pivotov: {len(df_daily) if df_daily is not None else 'None'} riadkov")
        return None, None

    prev_day_data = df_daily.iloc[-2]
    try:
        required_cols = ['high', 'low', 'close']
        for col in required_cols:
            if col not in prev_day_data or pd.isna(prev_day_data[col]):
                logger.error(f"[{instrument_symbol}] Chýbajúca alebo NaNs hodnota v stĺpci '{col}' pre výpočet pivotov. Dáta: {prev_day_data.to_dict() if isinstance(prev_day_data, pd.Series) else prev_day_data}")
                return None, None

        ph = float(prev_day_data['high'])
        pl = float(prev_day_data['low'])
        pc = float(prev_day_data['close'])

    except (TypeError, ValueError) as e:
        logger.error(f"[{instrument_symbol}] Neplatné cenové dáta (nie sú čísla) pre výpočet pivotov: {e}. Dáta: {prev_day_data.to_dict() if isinstance(prev_day_data, pd.Series) else prev_day_data}")
        return None, None

    rng = ph - pl
    if rng < -1e-9:
        logger.error(f"[{instrument_symbol}] Chyba pri výpočte pivotov: High ({ph}) < Low ({pl}). Preskakujem pivoty.")
        return None, None
    if abs(rng) < 1e-9:
        logger.warning(f"[{instrument_symbol}] Rozsah (Range) predchádzajúceho dňa je takmer 0 ({rng}). Pivoty budú rovné Close ({pc}).")
        return pc, pc

    h4 = pc + rng * 0.55
    l4 = pc - rng * 0.55

    price_format = ".4f" if instrument_symbol in ['M6A', 'M6B', 'M6E'] else ".2f"
    logger.info(f"[{instrument_symbol}] Camarilla pivoty: Predch.deň H={ph:{price_format}}, L={pl:{price_format}}, C={pc:{price_format}}, Rng={rng:{price_format}} => H4={h4:{price_format}}, L4={l4:{price_format}}")
    return h4, l4


def wait_for_next_bar(timeframe_setting: str) -> datetime:
    try:
        parts = timeframe_setting.split()
        if len(parts) != 2: raise ValueError("Formát TIMEFRAME musí byť 'hodnota jednotka'")
        value = int(parts[0])
        unit = parts[1].lower()
        if 'hour' in unit: tf_minutes = value * 60
        elif 'min' in unit: tf_minutes = value
        else: raise ValueError(f"Neznáma jednotka časového rámca v '{timeframe_setting}'")
    except Exception as e:
        logger.error(f"Chybný TIMEFRAME '{timeframe_setting}': {e}. Používam 60 minút.")
        tf_minutes = 60

    if tf_minutes <= 0:
        logger.error(f"Neplatný tf_minutes ({tf_minutes}). Používam 60 minút.")
        tf_minutes = 60

    try:
        local_tz_server = datetime.now().astimezone().tzinfo
        if local_tz_server is None:
            logger.warning("Nepodarilo sa automaticky zistiť lokálnu časovú zónu servera. Používam pytz.timezone('Europe/Bratislava'). Ak zlyhá, použijem UTC.")
            try:
                local_tz_server = pytz.timezone('Europe/Bratislava')
            except pytz.exceptions.UnknownTimeZoneError:
                logger.error("Časová zóna 'Europe/Bratislava' nebola nájdená, používam UTC ako fallback pre výpočet ďalšej sviečky.")
                local_tz_server = pytz.utc
        now_local_aware = datetime.now(local_tz_server)
    except Exception as e_tz_detect_wait:
        logger.error(f"Chyba pri získavaní lokálnej časovej zóny: {e_tz_detect_wait}. Používam naivný čas (môže viesť k problémom s DST).")
        now_local_aware = datetime.now()

    # Výpočet ďalšej sviečky založený na minútach od polnoci
    minutes_since_midnight_local = now_local_aware.hour * 60 + now_local_aware.minute
    current_bar_index_in_day = minutes_since_midnight_local // tf_minutes
    next_block_start_total_minutes = (current_bar_index_in_day + 1) * tf_minutes

    next_bar_hour_local = next_block_start_total_minutes // 60
    next_bar_minute_local = next_block_start_total_minutes % 60

    next_time_local_aware = now_local_aware.replace(
        hour=next_bar_hour_local % 24,
        minute=next_bar_minute_local,
        second=0,
        microsecond=0
    )

    days_to_add = next_block_start_total_minutes // (24 * 60) # Koľko celých dní sme preskočili
    if days_to_add > 0:
        next_time_local_aware += timedelta(days=days_to_add)

    # Ak je vypočítaný čas v minulosti alebo rovnaký pre aktuálny deň, posunieme
    while next_time_local_aware <= now_local_aware:
        logger.debug(f"wait_for_next_bar: next_time_local_aware ({next_time_local_aware}) je <= now_local_aware ({now_local_aware}), pripočítavam {tf_minutes} minút.")
        next_time_local_aware += timedelta(minutes=tf_minutes)


    if next_time_local_aware.tzinfo is None and local_tz_server is not None:
        logger.warning("next_time_local_aware stratilo časovú zónu, priraďujem zistenú lokálnu zónu.")
        try:
            next_time_local_aware = local_tz_server.localize(next_time_local_aware, is_dst=None)
        except Exception as e_localize_fallback:
             logger.error(f"Chyba pri núdzovej lokalizácii next_time_local_aware: {e_localize_fallback}. Používam UTC.")
             next_time_local_aware = pytz.utc.localize(datetime(next_time_local_aware.year, next_time_local_aware.month, next_time_local_aware.day,
                                                               next_time_local_aware.hour, next_time_local_aware.minute), is_dst=None)
    elif next_time_local_aware.tzinfo is None:
         logger.warning("next_time_local_aware je naivné a local_tz_server je None, lokalizujem na UTC ako núdzové riešenie.")
         next_time_local_aware = pytz.utc.localize(next_time_local_aware, is_dst=None)


    wait_seconds = (next_time_local_aware - now_local_aware).total_seconds()
    wait_seconds = max(wait_seconds, 0.1)

    max_sensible_wait = tf_minutes * 60 * 1.5
    if wait_seconds > max_sensible_wait and tf_minutes > 0 :
        logger.warning(f"Vypočítaný čas čakania ({wait_seconds:.2f}s) je dlhší ako 1.5x TF ({tf_minutes}min). Obmedzujem na 3 sekundy.")
        wait_seconds = 3.0

    next_time_for_log_str = next_time_local_aware.strftime('%Y-%m-%d %H:%M:%S %Z') if next_time_local_aware.tzinfo else next_time_local_aware.strftime('%Y-%m-%d %H:%M:%S')
    logger.info(f"Čakám {wait_seconds:.2f}s do ďalšej sviečky o {next_time_for_log_str} (čas servera).")
    time.sleep(wait_seconds)

    if next_time_local_aware.tzinfo is None: # Finálna poistka pred konverziou na UTC
        logger.error("KRITICKÁ CHYBA: next_time_local_aware je stále naivný pred finálnou konverziou na UTC!")
        # Núdzový pokus vytvoriť UTC čas pre ďalší interval
        # Toto by sa nemalo stať, ak je logika vyššie správna
        current_utc_for_fallback = datetime.now(pytz.utc)
        blocks_from_midnight_utc = (current_utc_for_fallback.hour * 60 + current_utc_for_fallback.minute) // tf_minutes
        next_total_minutes_utc_fb = (blocks_from_midnight_utc + 1) * tf_minutes # Premenované
        next_hour_utc_fb = next_total_minutes_utc_fb // 60
        next_minute_utc_fb = next_total_minutes_utc_fb % 60
        next_time_utc_val = current_utc_for_fallback.replace(hour=next_hour_utc_fb % 24, minute=next_minute_utc_fb, second=0, microsecond=0)
        days_offset_utc_val_fb = next_total_minutes_utc_fb // (24 * 60) # Premenované
        if days_offset_utc_val_fb > 0: next_time_utc_val += timedelta(days=days_offset_utc_val_fb)
        while next_time_utc_val <= current_utc_for_fallback: next_time_utc_val += timedelta(minutes=tf_minutes)
        next_time_utc = next_time_utc_val
    else:
        next_time_utc = next_time_local_aware.astimezone(pytz.utc)

    logger.info(f"Teoretický začiatok novej sviečky (UTC): {next_time_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    return next_time_utc

def is_near_market_close():
    if not getattr(config, 'CHECK_EOD_CLOSURE_ENABLED', False):
        logger.debug("EOD uzatváranie je vypnuté v konfigurácii.")
        return False
    try:
        eastern = pytz.timezone('US/Eastern')
    except pytz.exceptions.UnknownTimeZoneError:
        logger.error("Časové pásmo 'US/Eastern' nebolo nájdené. Skontrolujte inštaláciu pytz.")
        return False

    now_utc = datetime.now(pytz.utc)
    now_et = now_utc.astimezone(eastern)

    market_close_hour_et = getattr(config, 'MARKET_CLOSE_HOUR', 17)
    close_minutes_param = getattr(config, 'CLOSE_MINUTES_BEFORE_END', 5)

    if now_et.weekday() >= 5:
        logger.debug(f"Je víkend ({now_et.strftime('%A')}), EOD sa neuplatňuje.")
        return False

    # Vytvoríme close_time_et pre správny deň
    close_time_et = now_et.replace(hour=market_close_hour_et, minute=0, second=0, microsecond=0)

    # Ak je aktuálny čas už po zatváracom čase, posunieme close_time na ďalší deň
    if now_et >= close_time_et:
        close_time_et = close_time_et + timedelta(days=1)
        logger.debug(f"Aktuálny čas ET {now_et.strftime('%H:%M:%S')} je už po zatváracom čase, používam ďalší deň: {close_time_et.strftime('%Y-%m-%d %H:%M:%S')}.")

    time_diff_minutes = (close_time_et - now_et).total_seconds() / 60
    is_eod_window = 0 < time_diff_minutes <= close_minutes_param

    if time_diff_minutes < (close_minutes_param + 60) :
         logger.info(f"EOD Check: Aktuálny čas (ET): {now_et.strftime('%Y-%m-%d %H:%M:%S %Z')}, Zatvorenie trhu (ET): {close_time_et.strftime('%Y-%m-%d %H:%M:%S')}, Zostáva: {time_diff_minutes:.1f}min. V EOD okne ({close_minutes_param}min): {is_eod_window}")
    else:
         logger.debug(f"EOD Check: Zostáva {time_diff_minutes:.1f}min do close. Mimo detailného EOD logovacieho okna.")

    return is_eod_window
