#!/usr/bin/env python3
"""
Diagnostický script pre analýzu problémov s uzatváraním pozícií,
Telegram notifikáciami a CSV zápismi.

Analyzuje:
1. Prečo sa neposielajú Telegram notifikácie pri uzatváraní pozícií
2. Prečo sa nezapisujú obchody do trades_log.csv
3. Problém s reštartom bota a stratou kontextu pozícií
"""

import sys
import os
import logging
from datetime import datetime, timedelta
import pytz

# Pridanie PY script adresára do sys.path
script_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'PY script')
if script_dir not in sys.path:
    sys.path.insert(0, script_dir)

try:
    import config
    import utils
    from ib_insync import IB, util
except ImportError as e:
    print(f"Chyba pri importe: {e}")
    print("Uistite sa, že máte nainštalované všetky potrebné knižnice.")
    sys.exit(1)

def test_telegram_functionality():
    """Test Telegram notifikácií"""
    print("=== Test Telegram notifikácií ===")
    
    # Kontrola konfigurácie
    token = getattr(config, 'TELEGRAM_TOKEN', None)
    chat_id = getattr(config, 'TELEGRAM_CHAT_ID', None)
    
    print(f"Telegram token: {'✅ Nastavený' if token else '❌ Chýba'}")
    print(f"Chat ID: {'✅ Nastavený' if chat_id else '❌ Chýba'}")
    
    if not token or not chat_id:
        print("❌ Telegram konfigurácia nie je kompletná!")
        return False
    
    # Test odoslania správy
    test_message = f"🧪 Test správa z debug scriptu - {datetime.now().strftime('%H:%M:%S')}"
    print(f"\nPokúšam sa odoslať test správu: {test_message}")
    
    success = utils.send_telegram(test_message)
    if success:
        print("✅ Telegram test úspešný!")
        return True
    else:
        print("❌ Telegram test neúspešný!")
        return False

def test_csv_logging():
    """Test CSV logovania"""
    print("\n=== Test CSV logovania ===")
    
    csv_file = getattr(config, 'TRADES_CSV_FILE', 'trades_log.csv')
    print(f"CSV súbor: {csv_file}")
    
    # Kontrola existencie súboru
    if os.path.exists(csv_file):
        print(f"✅ CSV súbor existuje")
        # Zobrazenie posledných riadkov
        try:
            with open(csv_file, 'r') as f:
                lines = f.readlines()
                print(f"Počet riadkov v CSV: {len(lines)}")
                if len(lines) > 1:
                    print("Posledné 3 riadky:")
                    for line in lines[-3:]:
                        print(f"  {line.strip()}")
                else:
                    print("CSV obsahuje len header alebo je prázdny")
        except Exception as e:
            print(f"❌ Chyba pri čítaní CSV: {e}")
    else:
        print(f"❌ CSV súbor neexistuje")
    
    # Test zápisu
    print("\nTest zápisu do CSV...")
    test_success = utils.append_trade_to_csv(
        symbol='MNQ',
        signal='SHORT',
        entry_price=21375.5,
        exit_price=21382.5,
        pnl=-15.24,
        entry_time='2025-05-29 20:00:03 UTC',
        sl_level=21383.0,
        trail_activated=False
    )
    
    if test_success:
        print("✅ CSV test zápis úspešný!")
        return True
    else:
        print("❌ CSV test zápis neúspešný!")
        return False

def analyze_log_file():
    """Analýza log súboru"""
    print("\n=== Analýza log súboru ===")
    
    log_file = 'log.txt'
    if not os.path.exists(log_file):
        print(f"❌ Log súbor {log_file} neexistuje")
        return
    
    print(f"✅ Log súbor existuje")
    
    # Analýza kľúčových udalostí
    with open(log_file, 'r') as f:
        lines = f.readlines()
    
    print(f"Celkový počet riadkov v logu: {len(lines)}")
    
    # Hľadanie kľúčových udalostí
    mnq_events = []
    for i, line in enumerate(lines):
        if 'MNQ' in line and any(keyword in line for keyword in [
            'SHORT signál aktívny',
            'Pozícia SHORT potvrdená',
            'Pozícia sa javí ako uzavretá',
            'Uzavretý SHORT',
            'send_telegram',
            'append_trade_to_csv'
        ]):
            mnq_events.append((i+1, line.strip()))
    
    print(f"\nNájdené MNQ udalosti ({len(mnq_events)}):")
    for line_num, event in mnq_events:
        print(f"  Riadok {line_num}: {event}")
    
    # Hľadanie reštartov
    restart_events = []
    for i, line in enumerate(lines):
        if any(keyword in line for keyword in [
            'Pokúšam sa reštartovať',
            'Dočasne odpojený od IB',
            'Úspešne pripojený k IB'
        ]):
            restart_events.append((i+1, line.strip()))
    
    print(f"\nReštart udalosti ({len(restart_events)}):")
    for line_num, event in restart_events:
        print(f"  Riadok {line_num}: {event}")

def check_config_parameters():
    """Kontrola konfiguračných parametrov"""
    print("\n=== Kontrola konfigurácie ===")
    
    required_params = [
        'TELEGRAM_TOKEN',
        'TELEGRAM_CHAT_ID', 
        'TRADES_CSV_FILE',
        'POSITION_CONFIRM_WAIT_SECONDS',
        'POSITION_CONFIRM_ATTEMPTS'
    ]
    
    for param in required_params:
        value = getattr(config, param, None)
        if value is not None:
            if param in ['TELEGRAM_TOKEN', 'TELEGRAM_CHAT_ID']:
                print(f"✅ {param}: {'*' * 10} (skrytý)")
            else:
                print(f"✅ {param}: {value}")
        else:
            print(f"❌ {param}: CHÝBA!")

def simulate_position_closure():
    """Simulácia uzavretia pozície"""
    print("\n=== Simulácia uzavretia pozície ===")
    
    # Simulované dáta z logu
    symbol = 'MNQ'
    entry_signal = 'SHORT'
    entry_price = 21375.5
    exit_price = 21382.5
    pnl = -15.24
    entry_time = '2025-05-29 20:00:03 UTC'
    sl_price = 21383.0
    trailing_set = False
    
    print(f"Simulujem uzavretie pozície:")
    print(f"  Symbol: {symbol}")
    print(f"  Signál: {entry_signal}")
    print(f"  Entry: {entry_price}")
    print(f"  Exit: {exit_price}")
    print(f"  PNL: {pnl:.2f} USD")
    print(f"  SL: {sl_price}")
    
    # Test Telegram notifikácie
    msg = f"Uzavretý {entry_signal} na {symbol} @ {exit_price:.2f}, P/L {pnl:.2f} USD (cez stop-loss)"
    print(f"\nOdosielam Telegram správu: {msg}")
    telegram_success = utils.send_telegram(msg)
    print(f"Telegram: {'✅ Úspech' if telegram_success else '❌ Chyba'}")
    
    # Test CSV zápisu
    print(f"\nZapisujem do CSV...")
    csv_success = utils.append_trade_to_csv(
        symbol, entry_signal, entry_price, exit_price, pnl,
        entry_time, sl_price, trailing_set
    )
    print(f"CSV: {'✅ Úspech' if csv_success else '❌ Chyba'}")
    
    return telegram_success and csv_success

def main():
    """Hlavná funkcia diagnostiky"""
    print("🔍 DIAGNOSTIKA PROBLÉMOV S UZATVÁRANÍM POZÍCIÍ")
    print("=" * 60)
    
    # Nastavenie logovania
    utils.setup_logging()
    
    # Spustenie testov
    telegram_ok = test_telegram_functionality()
    csv_ok = test_csv_logging()
    check_config_parameters()
    analyze_log_file()
    simulation_ok = simulate_position_closure()
    
    print("\n" + "=" * 60)
    print("📊 SÚHRN VÝSLEDKOV:")
    print(f"  Telegram: {'✅ OK' if telegram_ok else '❌ PROBLÉM'}")
    print(f"  CSV logging: {'✅ OK' if csv_ok else '❌ PROBLÉM'}")
    print(f"  Simulácia: {'✅ OK' if simulation_ok else '❌ PROBLÉM'}")
    
    if not (telegram_ok and csv_ok):
        print("\n🚨 IDENTIFIKOVANÉ PROBLÉMY:")
        if not telegram_ok:
            print("  - Telegram notifikácie nefungujú")
        if not csv_ok:
            print("  - CSV logging nefunguje")
        
        print("\n💡 ODPORÚČANIA:")
        print("  1. Skontrolujte internetové pripojenie")
        print("  2. Overte Telegram token a chat ID")
        print("  3. Skontrolujte oprávnenia na zápis súborov")
        print("  4. Reštartujte trading bot po oprave")

if __name__ == '__main__':
    main()
