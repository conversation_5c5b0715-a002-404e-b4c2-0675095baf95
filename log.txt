May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:utils.py:393:utils:Teoretický začiatok novej sviečky (UTC): 2025-05-29 18:00:00 UTC
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:camarilla.py:168:__main__:Nov<PERSON> perióda začína. Teoretický začiatok novej sviečky (UTC): 2025-05-29 18:00:00 UTC
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:camarilla.py:178:__main__:Pre tento cyklus je očakávaný začiatok signáln<PERSON> (UTC): 2025-05-29 17:00:00 UTC
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:camarilla.py:284:__main__:--- <PERSON><PERSON><PERSON><PERSON><PERSON> spracovanie pre M2K pre sviečku končiacu pred 2025-05-29 18:00:00 UTC ---
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2074.6599121, marketValue=20746.6, averageCost=10456.12, unrealizedPNL=-165.64, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5920.0239258, marketValue=59200.24, averageCost=29693.12, unrealizedPNL=-186.0, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21444.5, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.********, marketValue=-6446.94, averageCost=6410.59, unrealizedPNL=-36.35, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2074.0600586, marketValue=20740.6, averageCost=10456.12, unrealizedPNL=-171.64, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5920.5, marketValue=59205.0, averageCost=29693.12, unrealizedPNL=-181.24, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21448.0996094, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.********, marketValue=-6443.65, averageCost=6410.59, unrealizedPNL=-33.06, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2075.0600586, marketValue=20750.6, averageCost=10456.12, unrealizedPNL=-161.64, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5922.7504883, marketValue=59227.5, averageCost=29693.12, unrealizedPNL=-158.74, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21456.1152344, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.********, marketValue=-6445.99, averageCost=6410.59, unrealizedPNL=-35.4, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2074.1401367, marketValue=20741.4, averageCost=10456.12, unrealizedPNL=-170.84, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5919.75, marketValue=59197.5, averageCost=29693.12, unrealizedPNL=-188.74, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21438.3496094, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.6444692, marketValue=-6444.69, averageCost=6410.59, unrealizedPNL=-34.1, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2073.********, marketValue=20734.0, averageCost=10456.12, unrealizedPNL=-178.24, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5918.5, marketValue=59185.0, averageCost=29693.12, unrealizedPNL=-201.24, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21431.75, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.********, marketValue=-6444.7, averageCost=6410.59, unrealizedPNL=-34.11, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2075.8400879, marketValue=20758.4, averageCost=10456.12, unrealizedPNL=-153.84, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5924.********, marketValue=59241.0, averageCost=29693.12, unrealizedPNL=-145.24, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21450.75, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.********, marketValue=-6446.1, averageCost=6410.59, unrealizedPNL=-35.51, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2074.6401367, marketValue=20746.4, averageCost=10456.12, unrealizedPNL=-165.84, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5920.********, marketValue=59202.49, averageCost=29693.12, unrealizedPNL=-183.75, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21431.1503906, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.********, marketValue=-6445.62, averageCost=6410.59, unrealizedPNL=-35.03, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2073.********, marketValue=20739.0, averageCost=10456.12, unrealizedPNL=-173.24, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5918.75, marketValue=59187.5, averageCost=29693.12, unrealizedPNL=-198.74, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21423.********, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.********, marketValue=-6444.95, averageCost=6410.59, unrealizedPNL=-34.36, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2073.********, marketValue=20739.26, averageCost=10456.12, unrealizedPNL=-172.98, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5919.0, marketValue=59190.0, averageCost=29693.12, unrealizedPNL=-196.24, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21427.0, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.6444853, marketValue=-6444.85, averageCost=6410.59, unrealizedPNL=-34.26, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2074.********, marketValue=20746.39, averageCost=10456.12, unrealizedPNL=-165.85, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5918.25, marketValue=59182.5, averageCost=29693.12, unrealizedPNL=-203.74, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21424.1503906, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.6446702, marketValue=-6446.7, averageCost=6410.59, unrealizedPNL=-36.11, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2073.********, marketValue=20732.6, averageCost=10456.12, unrealizedPNL=-179.64, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5914.75, marketValue=59147.5, averageCost=29693.12, unrealizedPNL=-238.74, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21406.5996094, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.6448373, marketValue=-6448.37, averageCost=6410.59, unrealizedPNL=-37.78, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2073.6401367, marketValue=20736.4, averageCost=10456.12, unrealizedPNL=-175.84, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5916.********, marketValue=59164.7, averageCost=29693.12, unrealizedPNL=-221.54, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21412.5, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.6449727, marketValue=-6449.73, averageCost=6410.59, unrealizedPNL=-39.14, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2074.********, marketValue=20744.0, averageCost=10456.12, unrealizedPNL=-168.24, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5918.0019531, marketValue=59180.02, averageCost=29693.12, unrealizedPNL=-206.22, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21420.6503906, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.********, marketValue=-6451.27, averageCost=6410.59, unrealizedPNL=-40.68, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2074.3400879, marketValue=20743.4, averageCost=10456.12, unrealizedPNL=-168.84, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5916.25, marketValue=59162.5, averageCost=29693.12, unrealizedPNL=-223.74, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21408.8496094, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.6451717, marketValue=-6451.72, averageCost=6410.59, unrealizedPNL=-41.13, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2073.********, marketValue=20734.6, averageCost=10456.12, unrealizedPNL=-177.64, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5911.75, marketValue=59117.5, averageCost=29693.12, unrealizedPNL=-268.74, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21386.5, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.6450616, marketValue=-6450.62, averageCost=6410.59, unrealizedPNL=-40.03, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2074.********, marketValue=20747.4, averageCost=10456.12, unrealizedPNL=-164.84, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5914.0, marketValue=59140.0, averageCost=29693.12, unrealizedPNL=-246.24, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21395.75, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.6451294, marketValue=-6451.29, averageCost=6410.59, unrealizedPNL=-40.7, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2073.********, marketValue=20733.0, averageCost=10456.12, unrealizedPNL=-179.24, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5912.0, marketValue=59120.0, averageCost=29693.12, unrealizedPNL=-266.24, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21389.0, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.********, marketValue=-6449.64, averageCost=6410.59, unrealizedPNL=-39.05, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2071.3400879, marketValue=20713.4, averageCost=10456.12, unrealizedPNL=-198.84, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5908.5, marketValue=59085.0, averageCost=29693.12, unrealizedPNL=-301.24, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21372.6171875, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.********, marketValue=-6448.62, averageCost=6410.59, unrealizedPNL=-38.03, realizedPNL=0.0, account='DUK870453')
May 29 20:00:00 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:00 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2106, reqId -1: HMDS data farm connection is OK:ushmds
May 29 20:00:01 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:01 INFO:utils.py:291:utils:[M2K] Camarilla pivoty: Predch.deň H=2097.60, L=2069.00, C=2070.20, Rng=28.60 => H4=2085.93, L4=2054.47
May 29 20:00:01 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:01 INFO:camarilla.py:366:__main__:[M2K] Kandidát na signálnu sviečku (iloc[-1]) začína (UTC): 2025-05-29 17:00:00 UTC
May 29 20:00:01 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:01 INFO:camarilla.py:369:__main__:[M2K] Signálna sviečka (po explicitnom endDateTime) zvolená: iloc[-1].
May 29 20:00:01 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:01 INFO:camarilla.py:407:__main__:[M2K] Signálna sviečka (2025-05-29 19:00 LOKAL): O=2073.80 C=2071.60 EMA8=2080.21 || H4d=2085.93 L4d=2054.47
May 29 20:00:01 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:01 INFO:camarilla.py:411:__main__:[M2K] Podmienky signálu: LONG=False, SHORT=False
May 29 20:00:01 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:01 WARNING:camarilla.py:419:__main__:[M2K] Nekonzistencia stavu pozície. Interný: False, IB: True. Aktualizujem.
May 29 20:00:01 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:01 INFO:camarilla.py:284:__main__:--- Začínam spracovanie pre MES pre sviečku končiacu pred 2025-05-29 18:00:00 UTC ---
May 29 20:00:01 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:01 INFO:utils.py:291:utils:[MES] Camarilla pivoty: Predch.deň H=5952.50, L=5891.50, C=5926.00, Rng=61.00 => H4=5959.55, L4=5892.45
May 29 20:00:02 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:02 INFO:camarilla.py:366:__main__:[MES] Kandidát na signálnu sviečku (iloc[-1]) začína (UTC): 2025-05-29 17:00:00 UTC
May 29 20:00:02 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:02 INFO:camarilla.py:369:__main__:[MES] Signálna sviečka (po explicitnom endDateTime) zvolená: iloc[-1].
May 29 20:00:02 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:02 INFO:camarilla.py:407:__main__:[MES] Signálna sviečka (2025-05-29 19:00 LOKAL): O=5921.50 C=5909.25 EMA8=5931.90 || H4d=5959.55 L4d=5892.45
May 29 20:00:02 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:02 INFO:camarilla.py:411:__main__:[MES] Podmienky signálu: LONG=False, SHORT=False
May 29 20:00:02 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:02 WARNING:camarilla.py:419:__main__:[MES] Nekonzistencia stavu pozície. Interný: False, IB: True. Aktualizujem.
May 29 20:00:02 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:02 INFO:camarilla.py:284:__main__:--- Začínam spracovanie pre MNQ pre sviečku končiacu pred 2025-05-29 18:00:00 UTC ---
May 29 20:00:02 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:02 INFO:utils.py:291:utils:[MNQ] Camarilla pivoty: Predch.deň H=21564.75, L=21322.50, C=21525.25, Rng=242.25 => H4=21658.49, L4=21392.01
May 29 20:00:02 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:02 INFO:camarilla.py:366:__main__:[MNQ] Kandidát na signálnu sviečku (iloc[-1]) začína (UTC): 2025-05-29 17:00:00 UTC
May 29 20:00:02 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:02 INFO:camarilla.py:369:__main__:[MNQ] Signálna sviečka (po explicitnom endDateTime) zvolená: iloc[-1].
May 29 20:00:02 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:02 INFO:camarilla.py:407:__main__:[MNQ] Signálna sviečka (2025-05-29 19:00 LOKAL): O=21456.25 C=21373.00 EMA8=21510.82 || H4d=21658.49 L4d=21392.01
May 29 20:00:02 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:02 INFO:camarilla.py:411:__main__:[MNQ] Podmienky signálu: LONG=False, SHORT=True
May 29 20:00:02 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:02 INFO:camarilla.py:433:__main__:[MNQ] SHORT signál aktívny. Overujem možnosť vstupu.
May 29 20:00:02 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:02 INFO:camarilla.py:436:__main__:[MNQ] Pokus o umiestnenie SHORT bracket príkazu s ref. cenou 21373.00
May 29 20:00:02 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:02 INFO:ib_interface.py:76:ib_interface:[MNQ] Používa sa tick size 0.25. SL diff: 10.00000, Trail act. diff: 5.00000, Trail offset (price): 0.25000
May 29 20:00:02 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:02 INFO:ib.py:678:ib_insync.ib:placeOrder: New order Trade(contract=Contract(secType='FUT', conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', exchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), order=MarketOrder(orderId=1448, clientId=1, action='SELL', totalQuantity=1, transmit=False, outsideRth=True), orderStatus=OrderStatus(orderId=1448, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 2, 893442, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
May 29 20:00:03 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:03 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2109, reqId 1448: Order Event Warning:Attribute 'Outside Regular Trading Hours' is ignored based on the order type and destination. PlaceOrder is now being processed.
May 29 20:00:03 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:03 INFO:ib_interface.py:113:ib_interface:[MNQ] Parent order ID: 1448, Stav: PendingSubmit
May 29 20:00:03 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:03 INFO:ib_interface.py:129:ib_interface:[MNQ] Vypočítaná SL cena: 21383.0, Zaokrúhlená SL cena: 21383.0 (des. miesta použité: 2)
May 29 20:00:03 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:03 INFO:ib.py:678:ib_insync.ib:placeOrder: New order Trade(contract=Contract(secType='FUT', conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', exchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), order=Order(orderId=1449, clientId=1, action='BUY', totalQuantity=1, orderType='STP', auxPrice=21383.0, parentId=1448, outsideRth=True), orderStatus=OrderStatus(orderId=1449, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 3, 397721, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
May 29 20:00:03 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:03 INFO:wrapper.py:440:ib_insync.wrapper:orderStatus: Trade(contract=Contract(secType='FUT', conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', exchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), order=Order(orderId=1449, clientId=1, permId=**********, action='BUY', totalQuantity=1.0, orderType='STP', lmtPrice=0.0, auxPrice=21383.0, parentId=1448, outsideRth=True), orderStatus=OrderStatus(orderId=1449, status='PreSubmitted', filled=0.0, remaining=1.0, avgFillPrice=0.0, permId=**********, parentId=1448, lastFillPrice=0.0, clientId=1, whyHeld='child,trigger', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 3, 397721, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0), TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 3, 555635, tzinfo=datetime.timezone.utc), status='PreSubmitted', message='', errorCode=0)], advancedError='')
May 29 20:00:03 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:03 INFO:wrapper.py:440:ib_insync.wrapper:orderStatus: Trade(contract=Contract(secType='FUT', conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', exchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), order=MarketOrder(orderId=1448, clientId=1, permId=**********, action='SELL', totalQuantity=1.0, lmtPrice=21432.5, auxPrice=0.0, transmit=False, outsideRth=True), orderStatus=OrderStatus(orderId=1448, status='PreSubmitted', filled=0.0, remaining=1.0, avgFillPrice=0.0, permId=**********, parentId=0, lastFillPrice=0.0, clientId=1, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 2, 893442, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0), TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 3, 575482, tzinfo=datetime.timezone.utc), status='PreSubmitted', message='', errorCode=0)], advancedError='')
May 29 20:00:03 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:03 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=-1.0, avgCost=42751.0)
May 29 20:00:03 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:03 INFO:wrapper.py:459:ib_insync.wrapper:execDetails Execution(execId='0000e1a7.68386e33.01.01', time=datetime.datetime(2025, 5, 29, 18, 0, 3, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='SLD', shares=1.0, price=21375.5, permId=**********, clientId=1, orderId=1448, liquidation=0, cumQty=1.0, avgPrice=21375.5, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1)
May 29 20:00:03 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:03 INFO:wrapper.py:487:ib_insync.wrapper:execDetails: Fill(contract=Contract(secType='FUT', conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', exchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), execution=Execution(execId='0000e1a7.68386e33.01.01', time=datetime.datetime(2025, 5, 29, 18, 0, 3, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='SLD', shares=1.0, price=21375.5, permId=**********, clientId=1, orderId=1448, liquidation=0, cumQty=1.0, avgPrice=21375.5, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1), commissionReport=CommissionReport(execId='', commission=0.0, currency='', realizedPNL=0.0, yield_=0.0, yieldRedemptionDate=0), time=datetime.datetime(2025, 5, 29, 18, 0, 3, 594677, tzinfo=datetime.timezone.utc))
May 29 20:00:03 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:03 INFO:wrapper.py:440:ib_insync.wrapper:orderStatus: Trade(contract=Contract(secType='FUT', conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', exchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), order=MarketOrder(orderId=1448, clientId=1, permId=**********, action='SELL', totalQuantity=1.0, lmtPrice=21432.5, auxPrice=0.0, transmit=False, outsideRth=True), orderStatus=OrderStatus(orderId=1448, status='Filled', filled=1.0, remaining=0.0, avgFillPrice=21375.5, permId=**********, parentId=0, lastFillPrice=21375.5, clientId=1, whyHeld='', mktCapPrice=0.0), fills=[Fill(contract=Contract(secType='FUT', conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', exchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), execution=Execution(execId='0000e1a7.68386e33.01.01', time=datetime.datetime(2025, 5, 29, 18, 0, 3, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='SLD', shares=1.0, price=21375.5, permId=**********, clientId=1, orderId=1448, liquidation=0, cumQty=1.0, avgPrice=21375.5, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1), commissionReport=CommissionReport(execId='', commission=0.0, currency='', realizedPNL=0.0, yield_=0.0, yieldRedemptionDate=0), time=datetime.datetime(2025, 5, 29, 18, 0, 3, 594677, tzinfo=datetime.timezone.utc))], log=[TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 2, 893442, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0), TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 3, 575482, tzinfo=datetime.timezone.utc), status='PreSubmitted', message='', errorCode=0), TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 3, 594677, tzinfo=datetime.timezone.utc), status='PreSubmitted', message='Fill 1.0@21375.5', errorCode=0), TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 3, 596537, tzinfo=datetime.timezone.utc), status='Filled', message='', errorCode=0)], advancedError='')
May 29 20:00:03 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:03 INFO:wrapper.py:504:ib_insync.wrapper:commissionReport: CommissionReport(execId='0000e1a7.68386e33.01.01', commission=0.62, currency='USD', realizedPNL=0.0, yield_=0.0, yieldRedemptionDate=0)
May 29 20:00:03 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:03 INFO:wrapper.py:440:ib_insync.wrapper:orderStatus: Trade(contract=Contract(secType='FUT', conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', exchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), order=Order(orderId=1449, clientId=1, permId=**********, action='BUY', totalQuantity=1.0, orderType='STP', lmtPrice=0.0, auxPrice=21383.0, parentId=1448, outsideRth=True), orderStatus=OrderStatus(orderId=1449, status='PreSubmitted', filled=0.0, remaining=1.0, avgFillPrice=0.0, permId=**********, parentId=1448, lastFillPrice=0.0, clientId=1, whyHeld='trigger', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 3, 397721, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0), TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 3, 555635, tzinfo=datetime.timezone.utc), status='PreSubmitted', message='', errorCode=0), TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 3, 601816, tzinfo=datetime.timezone.utc), status='PreSubmitted', message='', errorCode=0)], advancedError='')
May 29 20:00:03 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:03 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=-1.0, avgCost=42750.38)
May 29 20:00:03 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:03 INFO:ib_interface.py:171:ib_interface:[MNQ] SL order ID: 1449, Stav: PreSubmitted
May 29 20:00:03 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:03 INFO:ib_interface.py:198:ib_interface:[MNQ] Príkazy pre SHORT zadané. Ref_Entry @ 21373.00. SL @ 21383.00 (ID: 1449). Trail aktivácia @ 21368.00
May 29 20:00:09 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:09 INFO:camarilla.py:450:__main__:[MNQ] Pozícia SHORT potvrdená v ib.positions() (veľkosť: -1.0, pokus 1).
May 29 20:00:09 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:09 INFO:camarilla.py:460:__main__:[MNQ] Sledovanie SHORT pozície inicializované.
May 29 20:00:09 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:09 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2071.********, marketValue=20717.6, averageCost=10456.12, unrealizedPNL=-194.64, realizedPNL=0.0, account='DUK870453')
May 29 20:00:09 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:09 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5910.0, marketValue=59100.0, averageCost=29693.12, unrealizedPNL=-286.24, realizedPNL=0.0, account='DUK870453')
May 29 20:00:09 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:09 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=-1.0, marketPrice=21375.5996094, marketValue=-42751.2, averageCost=42750.38, unrealizedPNL=-0.82, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:09 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:09 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.6448655, marketValue=-6448.66, averageCost=6410.59, unrealizedPNL=-38.07, realizedPNL=0.0, account='DUK870453')
May 29 20:00:09 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:09 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2119, reqId -1: Market data farm is connecting:usfuture
May 29 20:00:10 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:10 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2104, reqId -1: Market data farm connection is OK:usfuture
May 29 20:00:10 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:10 INFO:camarilla.py:284:__main__:--- Začínam spracovanie pre MGC pre sviečku končiacu pred 2025-05-29 18:00:00 UTC ---
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:utils.py:291:utils:[MGC] Camarilla pivoty: Predch.deň H=3324.60, L=3274.50, C=3285.10, Rng=50.10 => H4=3312.65, L4=3257.55
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:camarilla.py:366:__main__:[MGC] Kandidát na signálnu sviečku (iloc[-1]) začína (UTC): 2025-05-29 17:00:00 UTC
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:camarilla.py:369:__main__:[MGC] Signálna sviečka (po explicitnom endDateTime) zvolená: iloc[-1].
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:camarilla.py:407:__main__:[MGC] Signálna sviečka (2025-05-29 19:00 LOKAL): O=3320.60 C=3322.40 EMA8=3310.30 || H4d=3312.65 L4d=3257.55
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:camarilla.py:411:__main__:[MGC] Podmienky signálu: LONG=False, SHORT=False
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:camarilla.py:727:__main__:Čakám 0 sekúnd pred ďalším pokusom o spustenie hlavnej slučky...
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:ib.py:290:ib_insync.ib:Disconnecting from 127.0.0.1:4001, 1.62 kB sent in 23 messages, 155 kB received in 2585 messages, session time 3.24 ks.
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:client.py:230:ib_insync.client:Disconnecting
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:camarilla.py:732:__main__:Dočasne odpojený od IB pred reštartovacím cyklom.
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:camarilla.py:737:__main__:Pokúšam sa reštartovať hlavnú slučku bota...
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:camarilla.py:652:__main__:Pokus o pripojenie k IB na 127.0.0.1:4001 s ClientID 1
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:camarilla.py:657:__main__:Pokus o pripojenie č. 1/15...
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:client.py:368:ib_insync.client:Disconnected.
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:client.py:204:ib_insync.client:Connecting to 127.0.0.1:4001 with clientId 1...
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:client.py:212:ib_insync.client:Connected
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:client.py:341:ib_insync.client:Logged on to server version 176
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2104, reqId -1: Market data farm connection is OK:usfuture
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2104, reqId -1: Market data farm connection is OK:usfarm
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2106, reqId -1: HMDS data farm connection is OK:ushmds
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2158, reqId -1: Sec-def data farm connection is OK:secdefil
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:client.py:218:ib_insync.client:API connection ready
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', multiplier='5', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, avgCost=29693.12)
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', multiplier='5', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, avgCost=10456.12)
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=-1.0, avgCost=42750.38)
May 29 20:00:11 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:11 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', multiplier='10000', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, avgCost=6410.59)
May 29 20:00:12 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:12 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2071.********, marketValue=20717.6, averageCost=10456.12, unrealizedPNL=-194.64, realizedPNL=0.0, account='DUK870453')
May 29 20:00:12 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:12 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.6448655, marketValue=-6448.66, averageCost=6410.59, unrealizedPNL=-38.07, realizedPNL=0.0, account='DUK870453')
May 29 20:00:12 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:12 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5910.0, marketValue=59100.0, averageCost=29693.12, unrealizedPNL=-286.24, realizedPNL=0.0, account='DUK870453')
May 29 20:00:12 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:12 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=-1.0, marketPrice=21375.5996094, marketValue=-42751.2, averageCost=42750.38, unrealizedPNL=-0.82, realizedPNL=-41.74, account='DUK870453')
May 29 20:00:28 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:28 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, avgCost=0.0)
May 29 20:00:28 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:28 INFO:wrapper.py:459:ib_insync.wrapper:execDetails Execution(execId='0000e1a7.68386e51.01.01', time=datetime.datetime(2025, 5, 29, 18, 0, 28, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='BOT', shares=1.0, price=21382.5, permId=**********, clientId=1, orderId=1449, liquidation=0, cumQty=1.0, avgPrice=21382.5, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1)
May 29 20:00:28 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:28 INFO:wrapper.py:487:ib_insync.wrapper:execDetails: Fill(contract=Contract(secType='FUT', conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', exchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), execution=Execution(execId='0000e1a7.68386e51.01.01', time=datetime.datetime(2025, 5, 29, 18, 0, 28, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='BOT', shares=1.0, price=21382.5, permId=**********, clientId=1, orderId=1449, liquidation=0, cumQty=1.0, avgPrice=21382.5, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1), commissionReport=CommissionReport(execId='', commission=0.0, currency='', realizedPNL=0.0, yield_=0.0, yieldRedemptionDate=0), time=datetime.datetime(2025, 5, 29, 18, 0, 28, 790480, tzinfo=datetime.timezone.utc))
May 29 20:00:28 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:28 INFO:wrapper.py:440:ib_insync.wrapper:orderStatus: Trade(contract=Contract(secType='FUT', conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', exchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), order=Order(orderId=1449, clientId=1, permId=**********, action='BUY', totalQuantity=1.0, orderType='STP', lmtPrice=0.0, auxPrice=21383.0, parentId=1448, outsideRth=True), orderStatus=OrderStatus(orderId=1449, status='Filled', filled=1.0, remaining=0.0, avgFillPrice=21382.5, permId=**********, parentId=1448, lastFillPrice=21382.5, clientId=1, whyHeld='', mktCapPrice=0.0), fills=[Fill(contract=Contract(secType='FUT', conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', exchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), execution=Execution(execId='0000e1a7.68386e51.01.01', time=datetime.datetime(2025, 5, 29, 18, 0, 28, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='BOT', shares=1.0, price=21382.5, permId=**********, clientId=1, orderId=1449, liquidation=0, cumQty=1.0, avgPrice=21382.5, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1), commissionReport=CommissionReport(execId='', commission=0.0, currency='', realizedPNL=0.0, yield_=0.0, yieldRedemptionDate=0), time=datetime.datetime(2025, 5, 29, 18, 0, 28, 790480, tzinfo=datetime.timezone.utc))], log=[TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 3, 397721, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0), TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 3, 555635, tzinfo=datetime.timezone.utc), status='PreSubmitted', message='', errorCode=0), TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 3, 601816, tzinfo=datetime.timezone.utc), status='PreSubmitted', message='', errorCode=0), TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 28, 790480, tzinfo=datetime.timezone.utc), status='PreSubmitted', message='Fill 1.0@21382.5', errorCode=0), TradeLogEntry(time=datetime.datetime(2025, 5, 29, 18, 0, 28, 792853, tzinfo=datetime.timezone.utc), status='Filled', message='', errorCode=0)], advancedError='')
May 29 20:00:28 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:28 INFO:wrapper.py:504:ib_insync.wrapper:commissionReport: CommissionReport(execId='0000e1a7.68386e51.01.01', commission=0.62, currency='USD', realizedPNL=-15.24, yield_=0.0, yieldRedemptionDate=0)
May 29 20:00:28 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:28 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, avgCost=0.0)
May 29 20:00:28 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:28 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, avgCost=0.0)
May 29 20:00:29 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:29 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2072.1401367, marketValue=20721.4, averageCost=10456.12, unrealizedPNL=-190.84, realizedPNL=0.0, account='DUK870453')
May 29 20:00:29 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:29 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5910.5283203, marketValue=59105.28, averageCost=29693.12, unrealizedPNL=-280.96, realizedPNL=0.0, account='DUK870453')
May 29 20:00:29 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:29 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21379.25, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-56.98, account='DUK870453')
May 29 20:00:29 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:29 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.********, marketValue=-6449.32, averageCost=6410.59, unrealizedPNL=-38.73, realizedPNL=0.0, account='DUK870453')
May 29 20:00:31 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:31 ERROR:ib.py:1779:ib_insync.ib:completed orders request timed out
May 29 20:00:31 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:31 INFO:wrapper.py:459:ib_insync.wrapper:execDetails Execution(execId='0000e1a7.6837961d.01.01', time=datetime.datetime(2025, 5, 29, 0, 0, 3, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='BOT', shares=1.0, price=21768.75, permId=**********, clientId=1, orderId=1429, liquidation=0, cumQty=1.0, avgPrice=21768.75, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1)
May 29 20:00:31 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:31 INFO:wrapper.py:459:ib_insync.wrapper:execDetails Execution(execId='0000e1a7.********.01.01', time=datetime.datetime(2025, 5, 29, 0, 0, 44, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='SLD', shares=1.0, price=21748.5, permId=**********, clientId=1, orderId=1430, liquidation=0, cumQty=1.0, avgPrice=21748.5, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1)
May 29 20:00:31 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:31 INFO:wrapper.py:459:ib_insync.wrapper:execDetails Execution(execId='0000e1a7.68386e33.01.01', time=datetime.datetime(2025, 5, 29, 18, 0, 3, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='SLD', shares=1.0, price=21375.5, permId=**********, clientId=1, orderId=1448, liquidation=0, cumQty=1.0, avgPrice=21375.5, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1)
May 29 20:00:31 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:31 INFO:wrapper.py:459:ib_insync.wrapper:execDetails Execution(execId='0000e1a7.68386e51.01.01', time=datetime.datetime(2025, 5, 29, 18, 0, 28, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='BOT', shares=1.0, price=21382.5, permId=**********, clientId=1, orderId=1449, liquidation=0, cumQty=1.0, avgPrice=21382.5, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1)
May 29 20:00:31 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:31 INFO:wrapper.py:504:ib_insync.wrapper:commissionReport: CommissionReport(execId='0000e1a7.6837961d.01.01', commission=0.62, currency='USD', realizedPNL=0.0, yield_=0.0, yieldRedemptionDate=0)
May 29 20:00:31 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:31 INFO:wrapper.py:504:ib_insync.wrapper:commissionReport: CommissionReport(execId='0000e1a7.********.01.01', commission=0.62, currency='USD', realizedPNL=-41.74, yield_=0.0, yieldRedemptionDate=0)
May 29 20:00:31 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:31 INFO:wrapper.py:504:ib_insync.wrapper:commissionReport: CommissionReport(execId='0000e1a7.68386e33.01.01', commission=0.62, currency='USD', realizedPNL=0.0, yield_=0.0, yieldRedemptionDate=0)
May 29 20:00:31 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:31 INFO:wrapper.py:504:ib_insync.wrapper:commissionReport: CommissionReport(execId='0000e1a7.68386e51.01.01', commission=0.62, currency='USD', realizedPNL=-15.24, yield_=0.0, yieldRedemptionDate=0)
May 29 20:00:31 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:31 INFO:ib.py:1789:ib_insync.ib:Synchronization complete
May 29 20:00:31 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:31 INFO:camarilla.py:660:__main__:Úspešne pripojený k IB.
May 29 20:00:31 ubuntu-4gb-fsn1-2 python[694438]: 2025-05-29 20:00:31 INFO:utils.py:373:utils:Čakám 3568.24s do ďalšej sviečky o 2025-05-29 21:00:00 CEST (čas servera).
